{"properties": [{"name": "bootystar", "type": "io.github.bootystar.starter.prop.BootystarProperties", "description": "Bootystar starter properties"}, {"name": "bootystar.time-format", "type": "java.lang.String", "defaultValue": "HH:mm:ss", "description": "Format for LocalTime"}, {"name": "bootystar.date-format", "type": "java.lang.String", "defaultValue": "yyyy-MM-dd", "description": "Format for LocalDate"}, {"name": "bootystar.date-time-format", "type": "java.lang.String", "defaultValue": "yyyy-MM-dd HH:mm:ss", "description": "Format for LocalDateTime and Date"}, {"name": "bootystar.time-zone-id", "type": "java.lang.String", "defaultValue": "GMT+8", "description": "Time zone ID for Date"}, {"name": "bootystar.aop", "type": "io.github.bootystar.starter.prop.support.AopProperties", "description": "AOP properties"}, {"name": "bootystar.aop.enabled", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Enable AOP auto-configuration"}, {"name": "bootystar.converter.enabled", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Enable converter auto-configuration"}, {"name": "bootystar.converter.string-to-date", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Enable String to Date converter"}, {"name": "bootystar.converter.string-to-local-date-time", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Enable String to LocalDateTime converter"}, {"name": "bootystar.converter.string-to-local-date", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Enable String to LocalDate converter"}, {"name": "bootystar.converter.string-to-local-time", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Enable String to LocalTime converter"}, {"name": "bootystar.converter.string-to-sql-date", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Enable String to SQL Date converter"}, {"name": "bootystar.converter.string-to-sql-time", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Enable String to SQL Time converter"}, {"name": "bootystar.converter.string-to-sql-timestamp", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Enable String to SQL Timestamp converter"}, {"name": "bootystar.jackson.enabled", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Enable Jackson auto-configuration"}, {"name": "bootystar.jackson.long-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Convert Long values to String during <PERSON> serialization"}, {"name": "bootystar.jackson.double-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Convert Double values to String during <PERSON> serialization"}, {"name": "bootystar.jackson.big-integer-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Convert BigInteger values to String during Jackson serialization"}, {"name": "bootystar.jackson.big-decimal-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Convert BigDecimal values to String during Jackson serialization"}, {"name": "bootystar.mybatis-plus.enabled", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Enable MyBatis-Plus auto-configuration"}, {"name": "bootystar.redis.enabled", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Enable Redis auto-configuration"}, {"name": "bootystar.excel.init-fast-excel-converter", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Add extra converters for FastExcel"}, {"name": "bootystar.excel.init-easy-excel-converter", "type": "java.lang.Bo<PERSON>an", "defaultValue": "false", "description": "Add extra converters for EasyExcel"}, {"name": "bootystar.excel.converter.big-decimal-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Write BigDecimal values as string"}, {"name": "bootystar.excel.converter.big-integer-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Write BigInteger values as string"}, {"name": "bootystar.excel.converter.long-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Write Long values as string"}, {"name": "bootystar.excel.converter.boolean-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Write Boolean values as string"}, {"name": "bootystar.excel.converter.float-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Write Float values as string"}, {"name": "bootystar.excel.converter.double-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Write Double values as string"}, {"name": "bootystar.excel.converter.sql-timestamp-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Write SQL Timestamp values as string"}, {"name": "bootystar.excel.converter.sql-date-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Write SQL Date values as string"}, {"name": "bootystar.excel.converter.sql-time-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Write SQL Time values as string"}, {"name": "bootystar.excel.converter.local-date-time-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Write LocalDateTime values as string"}, {"name": "bootystar.excel.converter.local-date-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Write LocalDate values as string"}, {"name": "bootystar.excel.converter.local-time-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Write LocalTime values as string"}, {"name": "bootystar.excel.converter.date-to-string", "type": "java.lang.Bo<PERSON>an", "defaultValue": "true", "description": "Write Date values as string"}]}